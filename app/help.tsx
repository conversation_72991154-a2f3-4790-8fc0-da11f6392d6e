import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Sc<PERSON>View,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Svg, { Path } from "react-native-svg";

const Colors = {
  primary: "#121712",
  secondary: "#638763",
  text: "#121712",
  textSecondary: "#638763",
  white: "#FFF",
  background: "#FFF",
  searchBackground: "#F0F5F0",
  cardBackground: "#FFF",
  border: "#F0F5F0",
  chipBackground: "#F0F5F0",
  activeTabBackground: "#FFF",
  shadow: "rgba(0, 0, 0, 0.10)",
};

// SVG Icons
const BackIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 18 16" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 8C18 8.41421 17.6642 8.75 17.25 8.75H2.56031L8.03063 14.2194C8.32368 14.5124 8.32368 14.9876 8.03063 15.2806C7.73757 15.5737 7.26243 15.5737 6.96937 15.2806L0.219375 8.53063C0.0785422 8.38995 -0.000590086 8.19906 -0.000590086 8C-0.000590086 7.80094 0.0785422 7.61005 0.219375 7.46937L6.96937 0.719375C7.26243 0.426319 7.73757 0.426319 8.03063 0.719375C8.32368 1.01243 8.32368 1.48757 8.03063 1.78062L2.56031 7.25H17.25C17.6642 7.25 18 7.58579 18 8Z"
      fill={Colors.primary}
    />
  </Svg>
);

const SearchIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.5306 18.4694L14.8366 13.7762C17.6629 10.383 17.3204 5.36693 14.0591 2.38935C10.7978 -0.588237 5.77134 -0.474001 2.64867 2.64867C-0.474001 5.77134 -0.588237 10.7978 2.38935 14.0591C5.36693 17.3204 10.383 17.6629 13.7762 14.8366L18.4694 19.5306C18.7624 19.8237 19.2376 19.8237 19.5306 19.5306C19.8237 19.2376 19.8237 18.7624 19.5306 18.4694ZM1.75 8.5C1.75 4.77208 4.77208 1.75 8.5 1.75C12.2279 1.75 15.25 4.77208 15.25 8.5C15.25 12.2279 12.2279 15.25 8.5 15.25C4.77379 15.2459 1.75413 12.2262 1.75 8.5Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const ArrowRightIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 10 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.03062 9.53063L1.53063 17.0306C1.23757 17.3237 0.762431 17.3237 0.469375 17.0306C0.176318 16.7376 0.176318 16.2624 0.469375 15.9694L7.43969 9L0.469375 2.03062C0.176318 1.73757 0.176318 1.26243 0.469375 0.969375C0.762431 0.676319 1.23757 0.676319 1.53063 0.969375L9.03062 8.46937C9.17146 8.61005 9.25059 8.80094 9.25059 9C9.25059 9.19906 9.17146 9.38995 9.03062 9.53063Z"
      fill={Colors.primary}
    />
  </Svg>
);

const HomeIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 19 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.75 8.83281V17.5C18.75 18.3284 18.0784 19 17.25 19H13.5C12.6716 19 12 18.3284 12 17.5V13.75C12 13.3358 11.6642 13 11.25 13H8.25C7.83579 13 7.5 13.3358 7.5 13.75V17.5C7.5 18.3284 6.82843 19 6 19H2.25C1.42157 19 0.75 18.3284 0.75 17.5V8.83281C0.749936 8.41309 0.92573 8.01254 1.23469 7.72844L8.73469 0.652188L8.745 0.641875C9.31719 0.121501 10.1912 0.121501 10.7634 0.641875C10.7666 0.645543 10.7701 0.648989 10.7738 0.652188L18.2738 7.72844C18.5796 8.01402 18.7522 8.41437 18.75 8.83281Z"
      fill={Colors.primary}
    />
  </Svg>
);

const CalendarIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 19 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.75 2H14.5V1.25C14.5 0.835786 14.1642 0.5 13.75 0.5C13.3358 0.5 13 0.835786 13 1.25V2H5.5V1.25C5.5 0.835786 5.16421 0.5 4.75 0.5C4.33579 0.5 4 0.835786 4 1.25V2H1.75C0.921573 2 0.25 2.67157 0.25 3.5V18.5C0.25 19.3284 0.921573 20 1.75 20H16.75C17.5784 20 18.25 19.3284 18.25 18.5V3.5C18.25 2.67157 17.5784 2 16.75 2ZM4 3.5V4.25C4 4.66421 4.33579 5 4.75 5C5.16421 5 5.5 4.66421 5.5 4.25V3.5H13V4.25C13 4.66421 13.3358 5 13.75 5C14.1642 5 14.5 4.66421 14.5 4.25V3.5H16.75V6.5H1.75V3.5H4ZM16.75 18.5H1.75V8H16.75V18.5ZM7.75 10.25V16.25C7.75 16.6642 7.41421 17 7 17C6.58579 17 6.25 16.6642 6.25 16.25V11.4631L5.83562 11.6713C5.4649 11.8566 5.01411 11.7063 4.82875 11.3356C4.64339 10.9649 4.79365 10.5141 5.16437 10.3287L6.66438 9.57875C6.89695 9.46237 7.17322 9.47478 7.39442 9.61155C7.61563 9.74832 7.75019 9.98993 7.75 10.25ZM13.2962 13.1047L11.5 15.5H13C13.4142 15.5 13.75 15.8358 13.75 16.25C13.75 16.6642 13.4142 17 13 17H10C9.71592 17 9.45622 16.8395 9.32918 16.5854C9.20214 16.3313 9.22955 16.0273 9.4 15.8L12.0981 12.2028C12.2653 11.9802 12.2955 11.6833 12.1764 11.4316C12.0573 11.1799 11.8086 11.0149 11.5304 11.003C11.2523 10.9912 10.9904 11.1344 10.8503 11.375C10.7202 11.6146 10.4703 11.7647 10.1976 11.7671C9.925 11.7694 9.67256 11.6236 9.53836 11.3863C9.40415 11.1489 9.40933 10.8574 9.55188 10.625C10.0612 9.74353 11.099 9.31391 12.0824 9.57743C13.0658 9.84095 13.7497 10.7319 13.75 11.75C13.7516 12.2391 13.5921 12.7152 13.2962 13.1047Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const MessageIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.875 10C10.875 10.6213 10.3713 11.125 9.75 11.125C9.12868 11.125 8.625 10.6213 8.625 10C8.625 9.37868 9.12868 8.875 9.75 8.875C10.3713 8.875 10.875 9.37868 10.875 10ZM5.625 8.875C5.00368 8.875 4.5 9.37868 4.5 10C4.5 10.6213 5.00368 11.125 5.625 11.125C6.24632 11.125 6.75 10.6213 6.75 10C6.75 9.37868 6.24632 8.875 5.625 8.875ZM13.875 8.875C13.2537 8.875 12.75 9.37868 12.75 10C12.75 10.6213 13.2537 11.125 13.875 11.125C14.4963 11.125 15 10.6213 15 10C15 9.37868 14.4963 8.875 13.875 8.875ZM19.5 10C19.5007 13.424 17.7053 16.5975 14.77 18.3605C11.8347 20.1234 8.18978 20.2174 5.1675 18.6081L1.97531 19.6722C1.43626 19.852 0.841913 19.7117 0.44011 19.3099C0.0383081 18.9081 -0.101955 18.3137 0.0778124 17.7747L1.14188 14.5825C-0.76326 11.0006 -0.251258 6.61332 2.42747 3.56638C5.10619 0.519438 9.39177 -0.550316 13.1882 0.880322C16.9846 2.31096 19.4983 5.94298 19.5 10ZM18 10C17.999 6.53154 15.8287 3.43408 12.569 2.24891C9.30932 1.06374 5.65643 2.04399 3.42801 4.70188C1.19959 7.35977 0.871575 11.1276 2.60719 14.1306C2.7147 14.3167 2.73723 14.5399 2.66906 14.7437L1.5 18.25L5.00625 17.0809C5.08262 17.0549 5.16275 17.0416 5.24344 17.0416C5.37516 17.0418 5.5045 17.0767 5.61844 17.1428C8.17111 18.6197 11.318 18.6217 13.8725 17.148C16.4271 15.6743 18.0007 12.9491 18 10Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const ProfileIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.8988 17.875C18.4709 15.4066 16.2706 13.6366 13.7028 12.7975C16.3135 11.2433 17.5641 8.13638 16.7582 5.2069C15.9522 2.27741 13.2883 0.247449 10.25 0.247449C7.21167 0.247449 4.54779 2.27741 3.74182 5.2069C2.93585 8.13638 4.18645 11.2433 6.79719 12.7975C4.22938 13.6356 2.02906 15.4056 0.60125 17.875C0.458704 18.1074 0.453527 18.3989 0.587731 18.6363C0.721935 18.8736 0.974375 19.0194 1.24702 19.0171C1.51967 19.0147 1.76958 18.8646 1.89969 18.625C3.66594 15.5725 6.78781 13.75 10.25 13.75C13.7122 13.75 16.8341 15.5725 18.6003 18.625C18.7304 18.8646 18.9803 19.0147 19.253 19.0171C19.5256 19.0194 19.7781 18.8736 19.9123 18.6363C20.0465 18.3989 20.0413 18.1074 19.8988 17.875ZM5 7C5 4.1005 7.3505 1.75 10.25 1.75C13.1495 1.75 15.5 4.1005 15.5 7C15.5 9.8995 13.1495 12.25 10.25 12.25C7.35179 12.2469 5.0031 9.89821 5 7Z"
      fill={Colors.secondary}
    />
  </Svg>
);

// Components
const SearchChip = ({ title, onPress }) => (
  <TouchableOpacity
    style={styles.searchChip}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text style={styles.searchChipText}>{title}</Text>
  </TouchableOpacity>
);

const TabButton = ({ title, isActive, onPress }) => (
  <TouchableOpacity
    style={[styles.tabButton, isActive && styles.activeTabButton]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text
      style={[styles.tabButtonText, isActive && styles.activeTabButtonText]}
    >
      {title}
    </Text>
  </TouchableOpacity>
);

const ServiceItem = ({ title, description, onPress }) => (
  <TouchableOpacity
    style={styles.serviceItem}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.serviceContent}>
      <Text style={styles.serviceTitle}>{title}</Text>
      <Text style={styles.serviceDescription}>{description}</Text>
    </View>
    <View style={styles.serviceImagePlaceholder} />
  </TouchableOpacity>
);

const VideoItem = ({ title, description, onPress }) => (
  <TouchableOpacity
    style={styles.videoItem}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.videoContent}>
      <Text style={styles.videoTitle}>{title}</Text>
      <Text style={styles.videoDescription}>{description}</Text>
    </View>
    <View style={styles.videoImagePlaceholder} />
  </TouchableOpacity>
);

const ExpertItem = ({ name, specialty, onPress }) => (
  <TouchableOpacity
    style={styles.expertItem}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.expertContent}>
      <Text style={styles.expertName}>{name}</Text>
      <Text style={styles.expertSpecialty}>{specialty}</Text>
    </View>
    <View style={styles.expertImagePlaceholder} />
  </TouchableOpacity>
);

const FAQItem = ({ question, onPress }) => (
  <TouchableOpacity
    style={styles.faqItem}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text style={styles.faqQuestion}>{question}</Text>
    <ArrowRightIcon />
  </TouchableOpacity>
);

export default function HelpScreen() {
  const router = useRouter();
  const [searchText, setSearchText] = useState("");
  const [activeTab, setActiveTab] = useState("Tümü");

  const handleBack = () => {
    console.log("Back button pressed - navigating to home");
    router.push("/");
  };

  const handleSearch = (text: string) => {
    setSearchText(text);
    console.log("Searching for:", text);
  };

  const handleChipPress = (chip: string) => {
    setSearchText(chip);
    console.log("Chip pressed:", chip);
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
    console.log("Tab pressed:", tab);
  };

  const handleItemPress = (item: string) => {
    console.log("Item pressed:", item);
  };

  const handleNavigation = (tab: string) => {
    if (tab === "Ana Sayfa") {
      router.push("/");
    } else {
      console.log(`Navigate to ${tab}`);
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Ara</Text>
          </View>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Search Bar */}
          <View style={styles.searchSection}>
            <View style={styles.searchContainer}>
              <View style={styles.searchIconContainer}>
                <SearchIcon />
              </View>
              <TextInput
                style={styles.searchInput}
                placeholder="Size Nasıl Yardımcı Olabiliriz?"
                placeholderTextColor={Colors.secondary}
                value={searchText}
                onChangeText={handleSearch}
              />
            </View>
          </View>

          {/* Search Chips */}
          <View style={styles.chipsSection}>
            <SearchChip
              title="gebelik takibi"
              onPress={() => handleChipPress("gebelik takibi")}
            />
            <SearchChip
              title="adet düzensizliği"
              onPress={() => handleChipPress("adet düzensizliği")}
            />
            <SearchChip
              title="online muayene"
              onPress={() => handleChipPress("online muayene")}
            />
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabsSection}>
            <View style={styles.tabsContainer}>
              <TabButton
                title="Tümü"
                isActive={activeTab === "Tümü"}
                onPress={() => handleTabPress("Tümü")}
              />
              <TabButton
                title="Hizmetler"
                isActive={activeTab === "Hizmetler"}
                onPress={() => handleTabPress("Hizmetler")}
              />
              <TabButton
                title="Videolar"
                isActive={activeTab === "Videolar"}
                onPress={() => handleTabPress("Videolar")}
              />
              <TabButton
                title="Uzmanlar"
                isActive={activeTab === "Uzmanlar"}
                onPress={() => handleTabPress("Uzmanlar")}
              />
            </View>
          </View>

          {/* Services Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Hizmetler</Text>
            <ServiceItem
              title="Online Doktor Görüşmesi"
              description="Uzman doktorlarla online görüşme imkanı"
              onPress={() => handleItemPress("Online Doktor Görüşmesi")}
            />
            <ServiceItem
              title="Gebelik Takibi"
              description="Gebelik sürecinizi uzmanlarla takip edin"
              onPress={() => handleItemPress("Gebelik Takibi")}
            />
          </View>

          {/* Educational Videos Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Eğitim Videoları</Text>
            <VideoItem
              title="Doğum Hazırlığı Eğitimi"
              description="Doğum sürecine hazırlık videoları"
              onPress={() => handleItemPress("Doğum Hazırlığı Eğitimi")}
            />
            <VideoItem
              title="Bebek Bakımı Eğitimi"
              description="Yenidoğan bebek bakımı hakkında bilgiler"
              onPress={() => handleItemPress("Bebek Bakımı Eğitimi")}
            />
          </View>

          {/* Experts Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Uzmanlar</Text>
            <ExpertItem
              name="Dr. Elif Öztürk"
              specialty="Kadın Hastalıkları ve Doğum Uzmanı"
              onPress={() => handleItemPress("Dr. Elif Öztürk")}
            />
            <ExpertItem
              name="Dr. Mehmet Demir"
              specialty="Perinatoloji Uzmanı"
              onPress={() => handleItemPress("Dr. Mehmet Demir")}
            />
          </View>

          {/* FAQ Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>SSS</Text>
            <FAQItem
              question="Online görüşme nasıl yapılır?"
              onPress={() => handleItemPress("Online görüşme nasıl yapılır?")}
            />
            <FAQItem
              question="Gebelik takibi ne zaman başlamalı?"
              onPress={() =>
                handleItemPress("Gebelik takibi ne zaman başlamalı?")
              }
            />
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <View style={styles.bottomNavigation}>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Ana Sayfa")}
            activeOpacity={0.7}
          >
            <HomeIcon />
            <Text style={styles.navText}>Ana Sayfa</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Randevular")}
            activeOpacity={0.7}
          >
            <CalendarIcon />
            <Text style={[styles.navText, styles.navTextSecondary]}>
              Randevular
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Mesajlar")}
            activeOpacity={0.7}
          >
            <MessageIcon />
            <Text style={[styles.navText, styles.navTextSecondary]}>
              Mesajlar
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Profil")}
            activeOpacity={0.7}
          >
            <ProfileIcon />
            <Text style={[styles.navText, styles.navTextSecondary]}>
              Profil
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.white,
  },
  backButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
    paddingRight: 48,
  },
  headerTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    textAlign: "center",
  },

  // Search Section
  searchSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 48,
    backgroundColor: Colors.searchBackground,
    borderRadius: 12,
  },
  searchIconContainer: {
    paddingLeft: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontFamily: "Lexend",
    fontSize: 15,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },

  // Chips Section
  chipsSection: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  searchChip: {
    height: 32,
    paddingHorizontal: 16,
    backgroundColor: Colors.chipBackground,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  searchChipText: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.text,
  },

  // Tabs Section
  tabsSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tabsContainer: {
    flexDirection: "row",
    height: 40,
    backgroundColor: Colors.chipBackground,
    borderRadius: 12,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 8,
  },
  activeTabButton: {
    backgroundColor: Colors.activeTabBackground,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButtonText: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },
  activeTabButtonText: {
    color: Colors.text,
  },

  // Content Sections
  contentSection: {
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  sectionTitle: {
    fontFamily: "Lexend",
    fontSize: 22,
    fontWeight: "700",
    lineHeight: 28,
    color: Colors.text,
    marginBottom: 12,
  },

  // Service Items
  serviceItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingVertical: 16,
    borderRadius: 12,
  },
  serviceContent: {
    flex: 1,
    paddingRight: 16,
    gap: 4,
  },
  serviceTitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
  },
  serviceDescription: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },
  serviceImagePlaceholder: {
    width: 130,
    height: 66,
    backgroundColor: "#E8D9DE",
    borderRadius: 12,
  },

  // Video Items
  videoItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingVertical: 16,
    borderRadius: 12,
  },
  videoContent: {
    flex: 1,
    paddingRight: 16,
    gap: 4,
  },
  videoTitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
  },
  videoDescription: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },
  videoImagePlaceholder: {
    width: 130,
    height: 64,
    backgroundColor: "#E8D9DE",
    borderRadius: 12,
  },

  // Expert Items
  expertItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingVertical: 16,
    borderRadius: 12,
  },
  expertContent: {
    flex: 1,
    paddingRight: 16,
    gap: 4,
  },
  expertName: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
  },
  expertSpecialty: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },
  expertImagePlaceholder: {
    width: 130,
    height: 66,
    backgroundColor: "#E8D9DE",
    borderRadius: 12,
  },

  // FAQ Items
  faqItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    minHeight: 56,
    paddingHorizontal: 0,
    backgroundColor: Colors.white,
  },
  faqQuestion: {
    flex: 1,
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },

  // Bottom Navigation
  bottomNavigation: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    gap: 4,
    paddingVertical: 8,
  },
  navText: {
    fontFamily: "Lexend",
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    color: Colors.text,
    textAlign: "center",
  },
  navTextSecondary: {
    color: Colors.secondary,
  },
});
