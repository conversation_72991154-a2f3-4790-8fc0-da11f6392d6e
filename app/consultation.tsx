import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Svg, { Path } from "react-native-svg";

const { width } = Dimensions.get("window");

const Colors = {
  primary: "#171217",
  secondary: "#806387",
  accent: "#B21AE5",
  text: "#171217",
  textSecondary: "#806387",
  white: "#FFF",
  background: "#FFF",
  inputBackground: "#F2F0F5",
  cardBackground: "#FFF",
  border: "#E3DBE5",
  progressBackground: "#E3DBE5",
  progressFill: "#171217",
  emergencyGradient: ["rgba(0, 0, 0, 0.00)", "rgba(0, 0, 0, 0.40)"],
};

// SVG Icons
const BackIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 18 16" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 8C18 8.41421 17.6642 8.75 17.25 8.75H2.56031L8.03063 14.2194C8.32368 14.5124 8.32368 14.9876 8.03063 15.2806C7.73757 15.5737 7.26243 15.5737 6.96937 15.2806L0.219375 8.53063C0.0785422 8.38995 -0.000590086 8.19906 -0.000590086 8C-0.000590086 7.80094 0.0785422 7.61005 0.219375 7.46937L6.96937 0.719375C7.26243 0.426319 7.73757 0.426319 8.03063 0.719375C8.32368 1.01243 8.32368 1.48757 8.03063 1.78062L2.56031 7.25H17.25C17.6642 7.25 18 7.58579 18 8Z"
      fill={Colors.primary}
    />
  </Svg>
);

const SettingsIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 5.5C7.51472 5.5 5.5 7.51472 5.5 10C5.5 12.4853 7.51472 14.5 10 14.5C12.4853 14.5 14.5 12.4853 14.5 10C14.4974 7.51579 12.4842 5.50258 10 5.5ZM10 13C8.34315 13 7 11.6569 7 10C7 8.34315 8.34315 7 10 7C11.6569 7 13 8.34315 13 10C13 11.6569 11.6569 13 10 13ZM18.25 10.2025C18.2537 10.0675 18.2537 9.9325 18.25 9.7975L19.6488 8.05C19.7975 7.86393 19.849 7.61827 19.7875 7.38813C19.5582 6.52619 19.2152 5.69861 18.7675 4.92719C18.6486 4.72249 18.4401 4.58592 18.205 4.55875L15.9813 4.31125C15.8888 4.21375 15.795 4.12 15.7 4.03L15.4375 1.80063C15.4101 1.56531 15.2732 1.35677 15.0681 1.23813C14.2964 0.791263 13.4689 0.448595 12.6072 0.219063C12.3769 0.157836 12.1312 0.209687 11.9453 0.35875L10.2025 1.75C10.0675 1.75 9.9325 1.75 9.7975 1.75L8.05 0.354063C7.86393 0.205326 7.61827 0.153827 7.38813 0.215312C6.52633 0.445025 5.6988 0.788016 4.92719 1.23531C4.72249 1.35417 4.58592 1.56268 4.55875 1.79781L4.31125 4.02531C4.21375 4.11844 4.12 4.21219 4.03 4.30656L1.80063 4.5625C1.56531 4.58988 1.35677 4.72682 1.23813 4.93188C0.791263 5.70359 0.448595 6.5311 0.219063 7.39281C0.157836 7.6231 0.209687 7.86878 0.35875 8.05469L1.75 9.7975C1.75 9.9325 1.75 10.0675 1.75 10.2025L0.354063 11.95C0.205326 12.1361 0.153827 12.3817 0.215312 12.6119C0.444615 13.4738 0.787627 14.3014 1.23531 15.0728C1.35417 15.2775 1.56268 15.4141 1.79781 15.4412L4.02156 15.6887C4.11469 15.7862 4.20844 15.88 4.30281 15.97L4.5625 18.1994C4.58988 18.4347 4.72682 18.6432 4.93188 18.7619C5.70359 19.2087 6.5311 19.5514 7.39281 19.7809C7.6231 19.8422 7.86878 19.7903 8.05469 19.6413L9.7975 18.25C9.9325 18.2537 10.0675 18.2537 10.2025 18.25L11.95 19.6488C12.1361 19.7975 12.3817 19.849 12.6119 19.7875C13.4738 19.5582 14.3014 19.2152 15.0728 18.7675C15.2775 18.6486 15.4141 18.4401 15.4412 18.205L15.6887 15.9813C15.7862 15.8888 15.88 15.795 15.97 15.7L18.1994 15.4375C18.4347 15.4101 18.6432 15.2732 18.7619 15.0681C19.2087 14.2964 19.5514 13.4689 19.7809 12.6072C19.8422 12.3769 19.7903 12.1312 19.6413 11.9453L18.25 10.2025ZM16.7406 9.59313C16.7566 9.86414 16.7566 10.1359 16.7406 10.4069C16.7295 10.5924 16.7876 10.7755 16.9037 10.9206L18.2341 12.5828C18.0814 13.0679 17.886 13.5385 17.65 13.9891L15.5312 14.2291C15.3467 14.2495 15.1764 14.3377 15.0531 14.4766C14.8727 14.6795 14.6805 14.8717 14.4775 15.0522C14.3387 15.1754 14.2505 15.3458 14.23 15.5303L13.9947 17.6472C13.5442 17.8833 13.0736 18.0787 12.5884 18.2313L10.9253 16.9009C10.7922 16.7946 10.6269 16.7367 10.4566 16.7369H10.4116C10.1405 16.7528 9.86883 16.7528 9.59781 16.7369C9.41226 16.7257 9.22918 16.7838 9.08406 16.9L7.41719 18.2313C6.93206 18.0786 6.46146 17.8831 6.01094 17.6472L5.77094 15.5312C5.75046 15.3467 5.66227 15.1764 5.52344 15.0531C5.32048 14.8727 5.12827 14.6805 4.94781 14.4775C4.82456 14.3387 4.6542 14.2505 4.46969 14.23L2.35281 13.9937C2.11674 13.5433 1.92128 13.0727 1.76875 12.5875L3.09906 10.9244C3.21522 10.7793 3.27336 10.5962 3.26219 10.4106C3.24625 10.1396 3.24625 9.86789 3.26219 9.59688C3.27336 9.41133 3.21522 9.22824 3.09906 9.08313L1.76875 7.41719C1.9214 6.93206 2.11685 6.46146 2.35281 6.01094L4.46875 5.77094C4.65326 5.75046 4.82362 5.66227 4.94688 5.52344C5.12733 5.32048 5.31954 5.12827 5.5225 4.94781C5.66188 4.82448 5.75043 4.65373 5.77094 4.46875L6.00625 2.35281C6.45672 2.11674 6.92733 1.92128 7.4125 1.76875L9.07563 3.09906C9.22074 3.21522 9.40383 3.27336 9.58937 3.26219C9.86039 3.24625 10.1321 3.24625 10.4031 3.26219C10.5887 3.27336 10.7718 3.21522 10.9169 3.09906L12.5828 1.76875C13.0679 1.9214 13.5385 2.11685 13.9891 2.35281L14.2291 4.46875C14.2495 4.65326 14.3377 4.82362 14.4766 4.94688C14.6795 5.12733 14.8717 5.31954 15.0522 5.5225C15.1754 5.66133 15.3458 5.74952 15.5303 5.77L17.6472 6.00531C17.8833 6.45578 18.0787 6.9264 18.2313 7.41156L16.9009 9.07469C16.7837 9.22103 16.7255 9.406 16.7378 9.59313H16.7406Z"
      fill={Colors.primary}
    />
  </Svg>
);

const MicIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 16 22" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 15.5C10.4842 15.4974 12.4974 13.4842 12.5 11V5C12.5 2.51472 10.4853 0.5 8 0.5C5.51472 0.5 3.5 2.51472 3.5 5V11C3.50258 13.4842 5.51579 15.4974 8 15.5ZM5 5C5 3.34315 6.34315 2 8 2C9.65685 2 11 3.34315 11 5V11C11 12.6569 9.65685 14 8 14C6.34315 14 5 12.6569 5 11V5ZM8.75 18.4625V20.75C8.75 21.1642 8.41421 21.5 8 21.5C7.58579 21.5 7.25 21.1642 7.25 20.75V18.4625C3.41988 18.0728 0.504731 14.8499 0.5 11C0.5 10.5858 0.835786 10.25 1.25 10.25C1.66421 10.25 2 10.5858 2 11C2 14.3137 4.68629 17 8 17C11.3137 17 14 14.3137 14 11C14 10.5858 14.3358 10.25 14.75 10.25C15.1642 10.25 15.5 10.5858 15.5 11C15.4953 14.8499 12.5801 18.0728 8.75 18.4625Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const HeartIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 22 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.6875 0C13.7516 0 12.0566 0.8325 11 2.23969C9.94344 0.8325 8.24844 0 6.3125 0C3.10384 0.00361657 0.503617 2.60384 0.5 5.8125C0.5 12.375 10.2303 17.6869 10.6447 17.9062C10.8665 18.0256 11.1335 18.0256 11.3553 17.9062C11.7697 17.6869 21.5 12.375 21.5 5.8125C21.4964 2.60384 18.8962 0.00361657 15.6875 0ZM11 16.3875C9.28813 15.39 2 10.8459 2 5.8125C2.0031 3.43206 3.93206 1.5031 6.3125 1.5C8.13594 1.5 9.66687 2.47125 10.3062 4.03125C10.4218 4.31259 10.6959 4.49627 11 4.49627C11.3041 4.49627 11.5782 4.31259 11.6938 4.03125C12.3331 2.46844 13.8641 1.5 15.6875 1.5C18.0679 1.5031 19.9969 3.43206 20 5.8125C20 10.8384 12.71 15.3891 11 16.3875Z"
      fill={Colors.primary}
    />
  </Svg>
);

const CalendarIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 18 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5 2H14.25V1.25C14.25 0.835786 13.9142 0.5 13.5 0.5C13.0858 0.5 12.75 0.835786 12.75 1.25V2H5.25V1.25C5.25 0.835786 4.91421 0.5 4.5 0.5C4.08579 0.5 3.75 0.835786 3.75 1.25V2H1.5C0.671573 2 0 2.67157 0 3.5V18.5C0 19.3284 0.671573 20 1.5 20H16.5C17.3284 20 18 19.3284 18 18.5V3.5C18 2.67157 17.3284 2 16.5 2ZM3.75 3.5V4.25C3.75 4.66421 4.08579 5 4.5 5C4.91421 5 5.25 4.66421 5.25 4.25V3.5H12.75V4.25C12.75 4.66421 13.0858 5 13.5 5C13.9142 5 14.25 4.66421 14.25 4.25V3.5H16.5V6.5H1.5V3.5H3.75ZM16.5 18.5H1.5V8H16.5V18.5ZM7.5 10.25V16.25C7.5 16.6642 7.16421 17 6.75 17C6.33579 17 6 16.6642 6 16.25V11.4631L5.58562 11.6713C5.2149 11.8566 4.76411 11.7063 4.57875 11.3356C4.39339 10.9649 4.54365 10.5141 4.91437 10.3287L6.41438 9.57875C6.64695 9.46237 6.92322 9.47478 7.14442 9.61155C7.36563 9.74832 7.50019 9.98993 7.5 10.25ZM13.0462 13.1047L11.25 15.5H12.75C13.1642 15.5 13.5 15.8358 13.5 16.25C13.5 16.6642 13.1642 17 12.75 17H9.75C9.46592 17 9.20622 16.8395 9.07918 16.5854C8.95214 16.3313 8.97955 16.0273 9.15 15.8L11.8481 12.2028C12.0153 11.9802 12.0455 11.6833 11.9264 11.4316C11.8073 11.1799 11.5586 11.0149 11.2804 11.003C11.0023 10.9912 10.7404 11.1344 10.6003 11.375C10.4702 11.6146 10.2203 11.7647 9.94765 11.7671C9.675 11.7694 9.42256 11.6236 9.28836 11.3863C9.15415 11.1489 9.15933 10.8574 9.30188 10.625C9.81124 9.74353 10.849 9.31391 11.8324 9.57743C12.8158 9.84095 13.4997 10.7319 13.5 11.75C13.5016 12.2391 13.3421 12.7152 13.0462 13.1047Z"
      fill={Colors.white}
    />
  </Svg>
);

// Components
const FilterChip = ({ title, isActive = false, onPress }) => (
  <TouchableOpacity
    style={[styles.filterChip, isActive && styles.filterChipActive]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text
      style={[styles.filterChipText, isActive && styles.filterChipTextActive]}
    >
      {title}
    </Text>
  </TouchableOpacity>
);

const DoctorCard = ({ name, specialty, rating, consultations, onPress }) => (
  <TouchableOpacity
    style={styles.doctorCard}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.doctorAvatar} />
    <View style={styles.doctorInfo}>
      <Text style={styles.doctorName}>{name}</Text>
      <Text style={styles.doctorSpecialty}>{specialty}</Text>
      <Text style={styles.doctorRating}>
        ★ {rating} ({consultations} consultations)
      </Text>
    </View>
  </TouchableOpacity>
);

export default function ConsultationScreen() {
  const router = useRouter();
  const [symptomsText, setSymptomsText] = useState("");
  const [activeFilter, setActiveFilter] = useState("Available Now");
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleBack = () => {
    console.log("Back button pressed - navigating to home");
    router.push("/");
  };

  const handleSettings = () => {
    console.log("Settings pressed");
  };

  const handleVoiceRecord = () => {
    console.log("Voice recording pressed");
  };

  const handleAnalyze = () => {
    if (symptomsText.trim()) {
      setIsAnalyzing(true);
      // Simulate analysis
      setTimeout(() => {
        setIsAnalyzing(false);
      }, 3000);
    }
  };

  const handleBookConsultation = () => {
    console.log("Book consultation pressed");
    router.push("/conference");
  };

  const handleDoctorPress = (doctorName: string) => {
    console.log(`Doctor pressed: ${doctorName}`);
    router.push("/conference");
  };

  const handleNavigation = (tab: string) => {
    if (tab === "Home") {
      router.push("/");
    } else {
      console.log(`Navigate to ${tab}`);
    }
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Online Consultation</Text>
          </View>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={handleSettings}
            activeOpacity={0.7}
          >
            <SettingsIcon />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Symptoms Input */}
          <View style={styles.inputSection}>
            <View style={styles.textInputContainer}>
              <TextInput
                style={styles.symptomsInput}
                placeholder="Describe your symptoms or concerns..."
                placeholderTextColor={Colors.text}
                value={symptomsText}
                onChangeText={setSymptomsText}
                multiline={true}
                textAlignVertical="top"
                maxLength={500}
                onEndEditing={handleAnalyze}
              />
            </View>
          </View>

          {/* Voice Recording */}
          <View style={styles.voiceSection}>
            <TouchableOpacity
              style={styles.voiceButton}
              onPress={handleVoiceRecord}
              activeOpacity={0.7}
            >
              <MicIcon />
              <Text style={styles.voiceCounter}>{symptomsText.length}/500</Text>
            </TouchableOpacity>
          </View>

          {/* Analysis Section */}
          {(symptomsText.length > 0 || isAnalyzing) && (
            <>
              <View style={styles.analysisSection}>
                <Text style={styles.analysisTitle}>
                  Analyzing your symptoms...
                </Text>
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressFill,
                        { width: isAnalyzing ? "60%" : "100%" },
                      ]}
                    />
                  </View>
                </View>
              </View>

              {/* Diagnosis Result */}
              {!isAnalyzing && (
                <View style={styles.diagnosisSection}>
                  <View style={styles.diagnosisCard}>
                    <View style={styles.diagnosisIcon}>
                      <HeartIcon />
                    </View>
                    <View style={styles.diagnosisInfo}>
                      <Text style={styles.diagnosisTitle}>
                        Women's Health & Obstetrics
                      </Text>
                      <Text style={styles.diagnosisConfidence}>
                        Confidence: 95%
                      </Text>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}

          {/* Available Doctors */}
          <View style={styles.doctorsSection}>
            <Text style={styles.sectionTitle}>Available Doctors</Text>

            {/* Filter Chips */}
            <View style={styles.filtersContainer}>
              <FilterChip
                title="Available Now"
                isActive={activeFilter === "Available Now"}
                onPress={() => setActiveFilter("Available Now")}
              />
              <FilterChip
                title="Highest Rated"
                isActive={activeFilter === "Highest Rated"}
                onPress={() => setActiveFilter("Highest Rated")}
              />
              <FilterChip
                title="Lowest Price"
                isActive={activeFilter === "Lowest Price"}
                onPress={() => setActiveFilter("Lowest Price")}
              />
            </View>

            {/* Doctor Cards */}
            <View style={styles.doctorsGrid}>
              <View style={styles.doctorsRow}>
                <DoctorCard
                  name="Dr. Amelia Carter"
                  specialty="Obstetrics & Gynecology, City Hospital"
                  rating="4.8"
                  consultations="120"
                  onPress={() => handleDoctorPress("Dr. Amelia Carter")}
                />
                <DoctorCard
                  name="Dr. Ethan Bennett"
                  specialty="Cardiology, University Medical Center"
                  rating="4.9"
                  consultations="150"
                  onPress={() => handleDoctorPress("Dr. Ethan Bennett")}
                />
              </View>
              <View style={styles.doctorsRow}>
                <DoctorCard
                  name="Dr. Olivia Hayes"
                  specialty="Pediatrics, Children's Hospital"
                  rating="4.7"
                  consultations="100"
                  onPress={() => handleDoctorPress("Dr. Olivia Hayes")}
                />
                <DoctorCard
                  name="Dr. Noah Parker"
                  specialty="Dermatology, Skin Care Clinic"
                  rating="4.6"
                  consultations="80"
                  onPress={() => handleDoctorPress("Dr. Noah Parker")}
                />
              </View>
            </View>
          </View>

          {/* Emergency Care */}
          <View style={styles.emergencySection}>
            <View style={styles.emergencyCard}>
              <LinearGradient
                colors={Colors.emergencyGradient}
                style={styles.emergencyGradient}
              >
                <View style={styles.emergencyContent}>
                  <View style={styles.emergencyTextContainer}>
                    <Text style={styles.emergencyTitle}>Need Urgent Care?</Text>
                    <Text style={styles.emergencySubtitle}>
                      24/7 Emergency Line
                    </Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
          </View>

          {/* Book Consultation Button */}
          <View style={styles.bookSection}>
            <TouchableOpacity
              style={styles.bookButton}
              onPress={handleBookConsultation}
              activeOpacity={0.8}
            >
              <CalendarIcon />
              <Text style={styles.bookButtonText}>Book Consultation</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <View style={styles.bottomNavigation}>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Home")}
            activeOpacity={0.7}
          >
            <Text style={styles.navText}>Home</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Messages")}
            activeOpacity={0.7}
          >
            <Text style={styles.navText}>Messages</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Profile")}
            activeOpacity={0.7}
          >
            <Text style={styles.navText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.white,
  },
  backButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    textAlign: "center",
  },
  settingsButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },

  // Input Section
  inputSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInputContainer: {
    minHeight: 144,
    backgroundColor: Colors.inputBackground,
    borderRadius: 12,
    padding: 16,
  },
  symptomsInput: {
    flex: 1,
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    textAlignVertical: "top",
  },

  // Voice Section
  voiceSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  voiceButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  voiceCounter: {
    fontFamily: "Lexend",
    fontSize: 13,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.secondary,
  },

  // Analysis Section
  analysisSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  analysisTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    marginBottom: 16,
  },
  progressContainer: {
    paddingHorizontal: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.progressBackground,
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: 8,
    backgroundColor: Colors.progressFill,
    borderRadius: 4,
  },

  // Diagnosis Section
  diagnosisSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  diagnosisCard: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    paddingVertical: 8,
    backgroundColor: Colors.white,
  },
  diagnosisIcon: {
    width: 48,
    height: 48,
    backgroundColor: Colors.inputBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  diagnosisInfo: {
    flex: 1,
  },
  diagnosisTitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },
  diagnosisConfidence: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },

  // Doctors Section
  doctorsSection: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    marginBottom: 8,
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 12,
    paddingVertical: 12,
    flexWrap: "wrap",
  },
  filterChip: {
    height: 32,
    paddingHorizontal: 16,
    backgroundColor: Colors.inputBackground,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  filterChipActive: {
    backgroundColor: Colors.primary,
  },
  filterChipText: {
    fontFamily: "Lexend",
    fontSize: 13,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.text,
  },
  filterChipTextActive: {
    color: Colors.white,
  },

  // Doctor Cards
  doctorsGrid: {
    gap: 12,
    paddingTop: 16,
  },
  doctorsRow: {
    flexDirection: "row",
    gap: 12,
  },
  doctorCard: {
    flex: 1,
    alignItems: "center",
    gap: 12,
    paddingBottom: 12,
  },
  doctorAvatar: {
    width: 141,
    height: 141,
    backgroundColor: "#5A8A9C",
    borderRadius: 70.5,
  },
  doctorInfo: {
    alignItems: "center",
  },
  doctorName: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
    textAlign: "center",
  },
  doctorSpecialty: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
    textAlign: "center",
    marginTop: 4,
  },
  doctorRating: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
    textAlign: "center",
    marginTop: 4,
  },

  // Emergency Section
  emergencySection: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  emergencyCard: {
    height: 201,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#5A8A9C",
  },
  emergencyGradient: {
    flex: 1,
    justifyContent: "flex-end",
  },
  emergencyContent: {
    padding: 16,
    paddingTop: 111,
  },
  emergencyTextContainer: {
    gap: 4,
  },
  emergencyTitle: {
    fontFamily: "Lexend",
    fontSize: 24,
    fontWeight: "700",
    lineHeight: 30,
    color: Colors.white,
  },
  emergencySubtitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.white,
  },

  // Book Section
  bookSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  bookButton: {
    flexDirection: "row",
    height: 56,
    backgroundColor: Colors.accent,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
    paddingHorizontal: 24,
  },
  bookButtonText: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: Colors.white,
    textAlign: "center",
  },

  // Bottom Navigation
  bottomNavigation: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: "#F2F0F5",
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 8,
  },
  navText: {
    fontFamily: "Lexend",
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    color: Colors.secondary,
    textAlign: "center",
  },
});
