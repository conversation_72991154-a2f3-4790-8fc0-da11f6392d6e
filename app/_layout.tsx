import { Stack, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import { Platform } from "react-native";

export default function RootLayout() {
  useEffect(() => {
    // Handle browser back button on web to prevent GO_BACK errors
    if (Platform.OS === "web") {
      const handleBrowserBack = (event: PopStateEvent) => {
        event.preventDefault();
        // Instead of allowing browser back, redirect to home
        router.push("/");
        return false;
      };

      // Add event listener for browser back button
      window.addEventListener("popstate", handleBrowserBack);

      // Cleanup event listener
      return () => {
        window.removeEventListener("popstate", handleBrowserBack);
      };
    }
  }, []);

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          // Disable swipe back gesture on mobile to prevent GO_BACK errors
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="login" options={{ headerShown: false }} />
        <Stack.Screen name="consultation" options={{ headerShown: false }} />
        <Stack.Screen name="conference" options={{ headerShown: false }} />
        <Stack.Screen name="help" options={{ headerShown: false }} />
        <Stack.Screen name="landing" options={{ headerShown: false }} />
        <Stack.Screen name="signup" options={{ headerShown: false }} />
      </Stack>
      <StatusBar style="dark" />
    </>
  );
}
