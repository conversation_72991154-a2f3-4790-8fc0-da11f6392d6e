import { useRouter } from "expo-router";
import React, { useState, useEffect } from "react";
import {
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Svg, { Path } from "react-native-svg";

const { width, height } = Dimensions.get("window");

const Colors = {
  dark: "#0F1A24",
  darkCard: "#21364A",
  blue: "#0D80F2",
  accent: "#8FADCC",
  white: "#FFF",
  overlay: "rgba(0, 0, 0, 0.40)",
  tabIndicator: "#304D69",
  transparent: "rgba(20, 20, 20, 0.40)",
};

// SVG Icons
const BackIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 18 16" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 8C18 8.41421 17.6642 8.75 17.25 8.75H2.56031L8.03063 14.2194C8.32368 14.5124 8.32368 14.9876 8.03063 15.2806C7.73757 15.5737 7.26243 15.5737 6.96937 15.2806L0.219375 8.53063C0.0785422 8.38995 -0.000590086 8.19906 -0.000590086 8C-0.000590086 7.80094 0.0785422 7.61005 0.219375 7.46937L6.96937 0.719375C7.26243 0.426319 7.73757 0.426319 8.03063 0.719375C8.32368 1.01243 8.32368 1.48757 8.03063 1.78062L2.56031 7.25H17.25C17.6642 7.25 18 7.58579 18 8Z"
      fill={Colors.white}
    />
  </Svg>
);

const PlayIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 17 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5 10C16.5013 10.518 16.2308 10.9987 15.7875 11.2666L2.28 19.5297C1.81627 19.8137 1.23518 19.8244 0.76125 19.5578C0.291875 19.2954 0.000784397 18.7999 0 18.2622V1.73781C0.000784397 1.20005 0.291875 0.704625 0.76125 0.442188C1.23518 0.175588 1.81627 0.186349 2.28 0.470313L15.7875 8.73344C16.2308 9.00131 16.5013 9.48202 16.5 10Z"
      fill={Colors.white}
    />
  </Svg>
);

const MicIcon = () => (
  <Svg width="20" height="20" viewBox="0 0 14 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 12.75C9.07018 12.7478 10.7478 11.0702 10.75 9V4C10.75 1.92893 9.07107 0.25 7 0.25C4.92893 0.25 3.25 1.92893 3.25 4V9C3.25215 11.0702 4.92982 12.7478 7 12.75ZM4.5 4C4.5 2.61929 5.61929 1.5 7 1.5C8.38071 1.5 9.5 2.61929 9.5 4V9C9.5 10.3807 8.38071 11.5 7 11.5C5.61929 11.5 4.5 10.3807 4.5 9V4ZM7.625 15.2188V17.125C7.625 17.4702 7.34518 17.75 7 17.75C6.65482 17.75 6.375 17.4702 6.375 17.125V15.2188C3.18323 14.894 0.753942 12.2082 0.75 9C0.75 8.65482 1.02982 8.375 1.375 8.375C1.72018 8.375 2 8.65482 2 9C2 11.7614 4.23858 14 7 14C9.76142 14 12 11.7614 12 9C12 8.65482 12.2798 8.375 12.625 8.375C12.9702 8.375 13.25 8.65482 13.25 9C13.2461 12.2082 10.8168 14.894 7.625 15.2188Z"
      fill={Colors.white}
    />
  </Svg>
);

const CameraIcon = () => (
  <Svg width="20" height="20" viewBox="0 0 18 14" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.8469 5.22969L8.09688 2.72969C7.90504 2.6017 7.65832 2.58975 7.45501 2.69859C7.2517 2.80743 7.12485 3.01939 7.125 3.25V8.25C7.12485 8.48061 7.2517 8.69256 7.45501 8.80141C7.65832 8.91025 7.90504 8.8983 8.09688 8.77031L11.8469 6.27031C12.021 6.15444 12.1256 5.95915 12.1256 5.75C12.1256 5.54085 12.021 5.34556 11.8469 5.22969ZM8.375 7.08203V4.42188L10.3734 5.75L8.375 7.08203ZM15.875 0.125H2.125C1.43464 0.125 0.875 0.684644 0.875 1.375V10.125C0.875 10.8154 1.43464 11.375 2.125 11.375H15.875C16.5654 11.375 17.125 10.8154 17.125 10.125V1.375C17.125 0.684644 16.5654 0.125 15.875 0.125ZM15.875 10.125H2.125V1.375H15.875V10.125ZM17.125 13.25C17.125 13.5952 16.8452 13.875 16.5 13.875H1.5C1.15482 13.875 0.875 13.5952 0.875 13.25C0.875 12.9048 1.15482 12.625 1.5 12.625H16.5C16.8452 12.625 17.125 12.9048 17.125 13.25Z"
      fill={Colors.white}
    />
  </Svg>
);

const PhoneIcon = () => (
  <Svg width="20" height="20" viewBox="0 0 17 17" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.3727 11.3797L11.6922 9.73047L11.682 9.72578C11.2939 9.55976 10.8481 9.60089 10.4969 9.83516C10.4765 9.8486 10.457 9.86321 10.4383 9.87891L8.53672 11.5C7.33203 10.9148 6.08828 9.68047 5.50313 8.49141L7.12656 6.56094C7.14219 6.54141 7.15703 6.52187 7.17109 6.50078C7.40027 6.15059 7.43898 5.70894 7.27422 5.32422V5.31484L5.62031 1.62812C5.40008 1.11992 4.87167 0.817244 4.32188 0.884375C2.13195 1.17255 0.496041 3.04119 0.5 5.25C0.5 11.4531 5.54688 16.5 11.75 16.5C13.9588 16.504 15.8275 14.8681 16.1156 12.6781C16.1829 12.1285 15.8806 11.6002 15.3727 11.3797ZM11.75 15.25C6.22965 15.244 1.75603 10.7703 1.75 5.25C1.74392 3.67182 2.90996 2.33424 4.47422 2.125C4.47391 2.12812 4.47391 2.13126 4.47422 2.13438L6.11484 5.80625L4.5 7.73906C4.48361 7.75792 4.46872 7.77804 4.45547 7.79922C4.21634 8.16615 4.18671 8.63147 4.37734 9.02578C5.08516 10.4734 6.54375 11.9211 8.00703 12.6281C8.40429 12.817 8.87158 12.7832 9.2375 12.5391C9.25756 12.5256 9.27686 12.5109 9.29531 12.4953L11.1945 10.875L14.8664 12.5195C14.8664 12.5195 14.8727 12.5195 14.875 12.5195C14.6683 14.0861 13.3301 15.2553 11.75 15.25Z"
      fill={Colors.white}
    />
  </Svg>
);

const PlusIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 18 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 9C18 9.41421 17.6642 9.75 17.25 9.75H9.75V17.25C9.75 17.6642 9.41421 18 9 18C8.58579 18 8.25 17.6642 8.25 17.25V9.75H0.75C0.335786 9.75 0 9.41421 0 9C0 8.58579 0.335786 8.25 0.75 8.25H8.25V0.75C8.25 0.335786 8.58579 0 9 0C9.41421 0 9.75 0.335786 9.75 0.75V8.25H17.25C17.6642 8.25 18 8.58579 18 9Z"
      fill={Colors.white}
    />
  </Svg>
);

// Control Button Component
const ControlButton = ({ icon, label, onPress, style = {} }) => (
  <TouchableOpacity
    style={[styles.controlButton, style]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.controlButtonInner}>{icon}</View>
    <Text style={styles.controlButtonLabel}>{label}</Text>
  </TouchableOpacity>
);

export default function ConferenceScreen() {
  const router = useRouter();
  const [minutes, setMinutes] = useState(23);
  const [seconds, setSeconds] = useState(45);
  const [isMuted, setIsMuted] = useState(false);

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setSeconds((prev) => {
        if (prev === 59) {
          setMinutes((m) => m + 1);
          return 0;
        }
        return prev + 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleBack = () => {
    console.log("Back button pressed - navigating to home");
    router.push("/");
  };

  const handleMute = () => {
    setIsMuted(!isMuted);
    console.log("Mute toggled:", !isMuted);
  };

  const handleCamera = () => {
    console.log("Camera toggled");
  };

  const handleEndCall = () => {
    console.log("Call ended - navigating to home");
    router.push("/");
  };

  const handleMoreOptions = () => {
    console.log("More options pressed");
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={Colors.dark} />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            activeOpacity={0.7}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Dr. Amelia Chen</Text>
          </View>
        </View>

        {/* Video Area */}
        <View style={styles.videoSection}>
          <LinearGradient
            colors={["#0D80F2", "#0D80F2"]}
            style={styles.videoGradient}
          >
            {/* Play Button Overlay */}
            <TouchableOpacity style={styles.playButton} activeOpacity={0.7}>
              <View style={styles.playButtonInner}>
                <PlayIcon />
              </View>
            </TouchableOpacity>
          </LinearGradient>
        </View>

        {/* Recording Indicator */}
        <View style={styles.recordingSection}>
          <Text style={styles.recordingText}>Recording in progress</Text>
        </View>

        {/* Timer Section */}
        <View style={styles.timerSection}>
          <View style={styles.timerColumn}>
            <View style={styles.timerCard}>
              <Text style={styles.timerNumber}>{minutes}</Text>
            </View>
            <Text style={styles.timerLabel}>Minutes</Text>
          </View>
          <View style={styles.timerColumn}>
            <View style={styles.timerCard}>
              <Text style={styles.timerNumber}>{seconds}</Text>
            </View>
            <Text style={styles.timerLabel}>Seconds</Text>
          </View>
        </View>

        {/* Transcription Section */}
        <View style={styles.transcriptionSection}>
          <Text style={styles.transcriptionTitle}>Real-time Transcription</Text>
          <Text style={styles.transcriptionText}>
            Dr. Chen: "Thank you for sharing your concerns, Sarah. It's
            important to address these symptoms comprehensively. Let's start
            with a detailed overview of your medical history and current
            medications."
          </Text>
        </View>

        {/* Control Buttons */}
        <View style={styles.controlsSection}>
          <ControlButton icon={<MicIcon />} label="Mute" onPress={handleMute} />
          <ControlButton
            icon={<CameraIcon />}
            label="Camera"
            onPress={handleCamera}
          />
          <ControlButton
            icon={<PhoneIcon />}
            label="End Call"
            onPress={handleEndCall}
          />
        </View>

        {/* Floating Action Button */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={handleMoreOptions}
          activeOpacity={0.7}
        >
          <PlusIcon />
        </TouchableOpacity>

        {/* Bottom Tab Indicator */}
        <View style={styles.bottomTabContainer}>
          <View style={styles.bottomTab}>
            <View style={styles.tabIndicator} />
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.dark,
  },
  backButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
    paddingRight: 48,
  },
  headerTitle: {
    fontFamily: "Manrope",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.white,
    textAlign: "center",
  },

  // Video Section
  videoSection: {
    height: 219,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.blue,
    position: "relative",
  },
  videoGradient: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  playButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.overlay,
    justifyContent: "center",
    alignItems: "center",
  },
  playButtonInner: {
    justifyContent: "center",
    alignItems: "center",
  },

  // Recording Section
  recordingSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: "center",
  },
  recordingText: {
    fontFamily: "Manrope",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.accent,
    textAlign: "center",
  },

  // Timer Section
  timerSection: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 24,
    gap: 16,
  },
  timerColumn: {
    flex: 1,
    alignItems: "center",
    gap: 16,
  },
  timerCard: {
    width: 171,
    height: 56,
    backgroundColor: Colors.darkCard,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  timerNumber: {
    fontFamily: "Manrope",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.white,
  },
  timerLabel: {
    fontFamily: "Manrope",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.white,
  },

  // Transcription Section
  transcriptionSection: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  transcriptionTitle: {
    fontFamily: "Manrope",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.white,
    marginBottom: 8,
  },
  transcriptionText: {
    fontFamily: "Manrope",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.white,
  },

  // Controls Section
  controlsSection: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-start",
    paddingHorizontal: 16,
    gap: 8,
  },
  controlButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
    gap: 8,
  },
  controlButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: Colors.darkCard,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  controlButtonLabel: {
    fontFamily: "Manrope",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.white,
    textAlign: "center",
  },

  // Floating Button
  floatingButton: {
    position: "absolute",
    bottom: 96,
    right: 20,
    width: 56,
    height: 56,
    backgroundColor: Colors.blue,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },

  // Bottom Tab
  bottomTabContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 96,
    height: 76,
    backgroundColor: Colors.transparent,
    justifyContent: "flex-end",
  },
  bottomTab: {
    backgroundColor: Colors.dark,
    paddingVertical: 10,
    alignItems: "center",
  },
  tabIndicator: {
    width: 36,
    height: 4,
    backgroundColor: Colors.tabIndicator,
    borderRadius: 2,
  },
});
